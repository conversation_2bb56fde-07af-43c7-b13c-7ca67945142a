import { auth } from '@clerk/nextjs/server';
import { redirect } from 'next/navigation';
import Link from 'next/link';
import { ArrowLeft, Plus, Edit, Trash2, Tag } from 'lucide-react';

export default async function CategoriesPage() {
  const { userId } = await auth();

  if (!userId) {
    redirect('/sign-in');
  }

  const defaultCategories = [
    { id: 1, name: 'Groceries', description: 'Food and household items', color: '#10B981', isDefault: true },
    { id: 2, name: 'Transportation', description: 'Gas, public transport, car maintenance', color: '#F59E0B', isDefault: true },
    { id: 3, name: 'Utilities', description: 'Electricity, water, internet, phone', color: '#3B82F6', isDefault: true },
    { id: 4, name: 'Entertainment', description: 'Movies, games, dining out', color: '#8B5CF6', isDefault: true },
    { id: 5, name: 'Healthcare', description: 'Medical expenses, pharmacy, insurance', color: '#EF4444', isDefault: true },
    { id: 6, name: 'Shopping', description: 'Clothing, electronics, general purchases', color: '#EC4899', isDefault: true },
    { id: 7, name: 'Education', description: 'Books, courses, training', color: '#06B6D4', isDefault: true },
    { id: 8, name: 'Other', description: 'Miscellaneous expenses', color: '#6B7280', isDefault: true },
  ];

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <Link 
              href="/dashboard"
              className="flex items-center text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white"
            >
              <ArrowLeft className="h-5 w-5 mr-2" />
              Back to Dashboard
            </Link>
          </div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Manage Categories
          </h1>
          <button className="bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors">
            <Plus className="h-5 w-5" />
            <span>Add Category</span>
          </button>
        </div>

        {/* Add Category Form */}
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg mb-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            Create New Category
          </h2>
          <form className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Category Name
              </label>
              <input
                type="text"
                placeholder="Enter category name"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Color
              </label>
              <input
                type="color"
                defaultValue="#3B82F6"
                className="w-full h-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 dark:border-gray-600"
              />
            </div>
            <div className="md:col-span-2 lg:col-span-1">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Description
              </label>
              <input
                type="text"
                placeholder="Optional description"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
            </div>
            <div className="flex items-end">
              <button
                type="submit"
                className="w-full bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-md transition-colors"
              >
                Create Category
              </button>
            </div>
          </form>
        </div>

        {/* Categories List */}
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            Your Categories
          </h2>
          
          <div className="space-y-3">
            {defaultCategories.map((category) => (
              <div key={category.id} className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                <div className="flex items-center space-x-4">
                  <div 
                    className="w-6 h-6 rounded-full"
                    style={{ backgroundColor: category.color }}
                  ></div>
                  <div>
                    <h3 className="font-semibold text-gray-900 dark:text-white">
                      {category.name}
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {category.description}
                    </p>
                  </div>
                  {category.isDefault && (
                    <span className="px-2 py-1 text-xs bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 rounded-full">
                      Default
                    </span>
                  )}
                </div>
                <div className="flex items-center space-x-2">
                  {!category.isDefault && (
                    <>
                      <button className="p-2 text-gray-600 hover:text-blue-600 dark:text-gray-400 dark:hover:text-blue-400">
                        <Edit className="h-4 w-4" />
                      </button>
                      <button className="p-2 text-gray-600 hover:text-red-600 dark:text-gray-400 dark:hover:text-red-400">
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </>
                  )}
                </div>
              </div>
            ))}
          </div>

          <div className="mt-6 text-center">
            <Tag className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600 dark:text-gray-400">
              You have {defaultCategories.length} categories available
            </p>
            <p className="text-sm text-gray-500 dark:text-gray-500">
              Create custom categories to better organize your expenses
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
