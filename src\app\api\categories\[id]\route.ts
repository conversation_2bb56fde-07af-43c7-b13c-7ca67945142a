import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { requireAuth } from '@/lib/auth';
import { query } from '@/lib/db';
import { updateCategoryEmbedding } from '@/lib/ai';
import { Category, ApiResponse, UpdateCategoryRequest } from '@/lib/types';

// Validation schema for updating categories
const updateCategorySchema = z.object({
  name: z.string().min(1).max(100).optional(),
  description: z.string().max(500).optional(),
  color: z.string().regex(/^#[0-9A-F]{6}$/i).optional(),
});

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await requireAuth();
    const categoryId = parseInt(params.id);

    if (isNaN(categoryId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid category ID' },
        { status: 400 }
      );
    }

    const result = await query<Category>(
      `SELECT * FROM categories 
       WHERE id = $1 AND (user_id = $2 OR is_default = true)`,
      [categoryId, user.id]
    );

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Category not found' },
        { status: 404 }
      );
    }

    const response: ApiResponse<Category> = {
      success: true,
      data: result.rows[0],
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error fetching category:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch category' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await requireAuth();
    const categoryId = parseInt(params.id);
    const body: UpdateCategoryRequest = await request.json();

    if (isNaN(categoryId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid category ID' },
        { status: 400 }
      );
    }

    const validatedData = updateCategorySchema.parse(body);

    // Check if category exists and belongs to user (can't edit default categories)
    const existingCategory = await query<Category>(
      'SELECT * FROM categories WHERE id = $1 AND user_id = $2 AND is_default = false',
      [categoryId, user.id]
    );

    if (existingCategory.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Category not found or cannot be edited' },
        { status: 404 }
      );
    }

    // Check if new name conflicts with existing categories
    if (validatedData.name) {
      const nameConflict = await query<Category>(
        `SELECT id FROM categories 
         WHERE name ILIKE $1 AND id != $2 AND (user_id = $3 OR is_default = true)`,
        [validatedData.name, categoryId, user.id]
      );

      if (nameConflict.rows.length > 0) {
        return NextResponse.json(
          { success: false, error: 'Category with this name already exists' },
          { status: 400 }
        );
      }
    }

    // Build dynamic update query
    const updateFields: string[] = [];
    const updateValues: any[] = [];
    let paramIndex = 1;

    if (validatedData.name !== undefined) {
      updateFields.push(`name = $${paramIndex}`);
      updateValues.push(validatedData.name);
      paramIndex++;
    }

    if (validatedData.description !== undefined) {
      updateFields.push(`description = $${paramIndex}`);
      updateValues.push(validatedData.description);
      paramIndex++;
    }

    if (validatedData.color !== undefined) {
      updateFields.push(`color = $${paramIndex}`);
      updateValues.push(validatedData.color);
      paramIndex++;
    }

    if (updateFields.length === 0) {
      return NextResponse.json(
        { success: false, error: 'No fields to update' },
        { status: 400 }
      );
    }

    // Add updated_at field
    updateFields.push(`updated_at = CURRENT_TIMESTAMP`);

    // Add WHERE conditions
    updateValues.push(categoryId, user.id);

    const updateQuery = `
      UPDATE categories 
      SET ${updateFields.join(', ')}
      WHERE id = $${paramIndex} AND user_id = $${paramIndex + 1}
      RETURNING *
    `;

    const result = await query<Category>(updateQuery, updateValues);
    const updatedCategory = result.rows[0];

    // Update embedding if name or description changed (async, non-blocking)
    if (validatedData.name || validatedData.description !== undefined) {
      updateCategoryEmbedding(
        categoryId,
        updatedCategory.name,
        updatedCategory.description || undefined
      ).catch(console.error);
    }

    const response: ApiResponse<Category> = {
      success: true,
      data: updatedCategory,
    };

    return NextResponse.json(response);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation failed',
          errors: error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message,
          })),
        },
        { status: 400 }
      );
    }

    console.error('Error updating category:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update category' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await requireAuth();
    const categoryId = parseInt(params.id);

    if (isNaN(categoryId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid category ID' },
        { status: 400 }
      );
    }

    // Check if category exists and belongs to user (can't delete default categories)
    const existingCategory = await query<Category>(
      'SELECT * FROM categories WHERE id = $1 AND user_id = $2 AND is_default = false',
      [categoryId, user.id]
    );

    if (existingCategory.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Category not found or cannot be deleted' },
        { status: 404 }
      );
    }

    // Check if category is being used by any expenses
    const expenseCount = await query<{ count: string }>(
      'SELECT COUNT(*) as count FROM expenses WHERE category_id = $1',
      [categoryId]
    );

    if (parseInt(expenseCount.rows[0].count) > 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Cannot delete category that is being used by expenses. Please reassign or delete those expenses first.' 
        },
        { status: 400 }
      );
    }

    const result = await query(
      'DELETE FROM categories WHERE id = $1 AND user_id = $2 RETURNING id',
      [categoryId, user.id]
    );

    const response: ApiResponse = {
      success: true,
      data: { message: 'Category deleted successfully' },
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error deleting category:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete category' },
      { status: 500 }
    );
  }
}
