'use client';

import { useEffect, useState } from 'react';

interface HydrationBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

/**
 * Component to handle hydration mismatches gracefully
 * Prevents hydration warnings from browser extensions
 */
export function HydrationBoundary({ children, fallback }: HydrationBoundaryProps) {
  const [isHydrated, setIsHydrated] = useState(false);

  useEffect(() => {
    // Suppress hydration warnings in development
    if (process.env.NODE_ENV === 'development') {
      const originalError = console.error;
      console.error = (...args) => {
        if (
          typeof args[0] === 'string' &&
          args[0].includes('hydrated but some attributes') ||
          args[0].includes('Hydration failed') ||
          args[0].includes('There was an error while hydrating')
        ) {
          // Suppress hydration warnings
          return;
        }
        originalError.apply(console, args);
      };
    }

    setIsHydrated(true);
  }, []);

  if (!isHydrated) {
    return fallback || null;
  }

  return <>{children}</>;
}

/**
 * Hook to safely check if component is hydrated
 */
export function useIsHydrated() {
  const [isHydrated, setIsHydrated] = useState(false);

  useEffect(() => {
    setIsHydrated(true);
  }, []);

  return isHydrated;
}
