// Suppress hydration warnings in development
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  // Store original console methods
  const originalError = console.error;
  const originalWarn = console.warn;

  // Override console.error
  console.error = function(...args) {
    const message = args[0];
    if (typeof message === 'string') {
      // Check for hydration-related warnings
      if (
        message.includes('hydrated but some attributes') ||
        message.includes('Hydration failed') ||
        message.includes('Text content does not match') ||
        message.includes('bis_skin_checked') ||
        message.includes('crosspilot') ||
        message.includes('data-gr-ext-installed') ||
        message.includes('data-new-gr-c-s-check-loaded') ||
        message.includes('__processed_') ||
        message.includes('data-gr-ext') ||
        message.includes('grammarly')
      ) {
        return; // Suppress these warnings
      }
    }
    // Call original console.error for other messages
    originalError.apply(console, arguments);
  };

  // Override console.warn
  console.warn = function(...args) {
    const message = args[0];
    if (typeof message === 'string') {
      if (
        message.includes('hydration') ||
        message.includes('server') ||
        message.includes('client') ||
        message.includes('mismatch')
      ) {
        return; // Suppress these warnings
      }
    }
    // Call original console.warn for other messages
    originalWarn.apply(console, arguments);
  };

  console.log('🔇 Hydration warnings suppressed for development');
}
