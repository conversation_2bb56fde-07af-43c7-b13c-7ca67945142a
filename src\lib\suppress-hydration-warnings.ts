/**
 * Suppress hydration warnings caused by browser extensions
 * This should only run in development mode
 */
export function suppressHydrationWarnings() {
  if (typeof window === 'undefined' || process.env.NODE_ENV !== 'development') {
    return;
  }

  // Store original console.error
  const originalError = console.error;

  // Override console.error to filter out hydration warnings
  console.error = (...args: any[]) => {
    // Check if this is a hydration warning
    const isHydrationWarning = args.some((arg) => {
      if (typeof arg === 'string') {
        return (
          arg.includes('hydrated but some attributes') ||
          arg.includes('Hydration failed') ||
          arg.includes('There was an error while hydrating') ||
          arg.includes('Text content does not match') ||
          arg.includes('suppressHydrationWarning') ||
          arg.includes('bis_skin_checked') ||
          arg.includes('crosspilot') ||
          arg.includes('data-gr-ext-installed') ||
          arg.includes('data-new-gr-c-s-check-loaded') ||
          arg.includes('__processed_')
        );
      }
      return false;
    });

    // Only log if it's not a hydration warning
    if (!isHydrationWarning) {
      originalError.apply(console, args);
    }
  };

  // Also suppress React DevTools hydration warnings
  if (window.React && window.React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED) {
    const internals = window.React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;
    if (internals.ReactDebugCurrentFrame) {
      const originalGetStackAddendum = internals.ReactDebugCurrentFrame.getStackAddendum;
      internals.ReactDebugCurrentFrame.getStackAddendum = function() {
        try {
          return originalGetStackAddendum.apply(this, arguments);
        } catch (e) {
          return '';
        }
      };
    }
  }
}

// Auto-run in development
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  suppressHydrationWarnings();
}
