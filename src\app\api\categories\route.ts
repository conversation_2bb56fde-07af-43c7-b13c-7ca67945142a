import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { requireAuth } from '@/lib/auth';
import { query } from '@/lib/db';
import { updateCategoryEmbedding } from '@/lib/ai';
import { generateRandomColor } from '@/lib/utils';
import { Category, ApiResponse, CreateCategoryRequest } from '@/lib/types';

// Validation schema for creating categories
const createCategorySchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().max(500).optional(),
  color: z.string().regex(/^#[0-9A-F]{6}$/i).optional(),
});

export async function GET(request: NextRequest) {
  try {
    const user = await requireAuth();

    // Get all categories (default + user's custom categories)
    const result = await query<Category>(
      `SELECT * FROM categories 
       WHERE is_default = true OR user_id = $1
       ORDER BY is_default DESC, name ASC`,
      [user.id]
    );

    const response: ApiResponse<Category[]> = {
      success: true,
      data: result.rows,
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error fetching categories:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch categories' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const user = await requireAuth();
    const body: CreateCategoryRequest = await request.json();
    
    const validatedData = createCategorySchema.parse(body);

    // Check if category name already exists for this user
    const existingCategory = await query<Category>(
      `SELECT id FROM categories 
       WHERE name ILIKE $1 AND (user_id = $2 OR is_default = true)`,
      [validatedData.name, user.id]
    );

    if (existingCategory.rows.length > 0) {
      return NextResponse.json(
        { success: false, error: 'Category with this name already exists' },
        { status: 400 }
      );
    }

    // Generate color if not provided
    const color = validatedData.color || generateRandomColor();

    // Create the category
    const result = await query<Category>(
      `INSERT INTO categories (name, description, color, user_id, is_default)
       VALUES ($1, $2, $3, $4, false)
       RETURNING *`,
      [validatedData.name, validatedData.description || '', color, user.id]
    );

    const category = result.rows[0];

    // Generate embedding for the category (async, non-blocking)
    updateCategoryEmbedding(
      category.id,
      category.name,
      category.description || undefined
    ).catch(console.error);

    const response: ApiResponse<Category> = {
      success: true,
      data: category,
    };

    return NextResponse.json(response, { status: 201 });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation failed',
          errors: error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message,
          })),
        },
        { status: 400 }
      );
    }

    console.error('Error creating category:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create category' },
      { status: 500 }
    );
  }
}
