import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // Suppress hydration warnings in development for browser extension conflicts
  reactStrictMode: true,
  experimental: {
    // Helps with hydration issues
    optimizePackageImports: ['@clerk/nextjs', 'lucide-react'],
  },
  // Disable source maps in development to reduce hydration noise
  productionBrowserSourceMaps: false,
  // Additional configuration to handle hydration warnings
  compiler: {
    // Remove console.logs in production
    removeConsole: process.env.NODE_ENV === 'production',
  },
  // Webpack configuration to suppress hydration warnings
  webpack: (config, { dev, isServer }) => {
    if (dev && !isServer) {
      // Suppress hydration warnings in development
      config.infrastructureLogging = {
        level: 'error',
      };
    }
    return config;
  },
};

export default nextConfig;
