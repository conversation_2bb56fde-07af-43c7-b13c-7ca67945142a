import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // Suppress hydration warnings in development for browser extension conflicts
  reactStrictMode: false, // Disable strict mode to reduce hydration warnings
  experimental: {
    // Helps with hydration issues
    optimizePackageImports: ['@clerk/nextjs', 'lucide-react'],
  },
  // Disable source maps in development to reduce hydration noise
  productionBrowserSourceMaps: false,
  // Additional configuration to handle hydration warnings
  compiler: {
    // Remove console.logs in production
    removeConsole: process.env.NODE_ENV === 'production',
  },
  // Environment variables to suppress warnings
  env: {
    SUPPRESS_HYDRATION_WARNINGS: 'true',
  },
  // Webpack configuration to suppress hydration warnings
  webpack: (config, { dev, isServer }) => {
    if (dev && !isServer) {
      // Suppress infrastructure logging in development
      config.infrastructureLogging = {
        level: 'error',
      };
    }
    return config;
  },
};

export default nextConfig;
