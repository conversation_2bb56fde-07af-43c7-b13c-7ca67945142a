import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // Suppress hydration warnings in development for browser extension conflicts
  reactStrictMode: false, // Disable strict mode to reduce hydration warnings
  experimental: {
    // Helps with hydration issues
    optimizePackageImports: ['@clerk/nextjs', 'lucide-react'],
  },
  // Disable source maps in development to reduce hydration noise
  productionBrowserSourceMaps: false,
  // Additional configuration to handle hydration warnings
  compiler: {
    // Remove console.logs in production
    removeConsole: process.env.NODE_ENV === 'production',
  },
  // Environment variables to suppress warnings
  env: {
    SUPPRESS_HYDRATION_WARNINGS: 'true',
  },
  // Webpack configuration to suppress hydration warnings
  webpack: (config, { dev, isServer }) => {
    if (dev) {
      // Suppress all console warnings in development
      config.infrastructureLogging = {
        level: 'error',
      };

      // Add plugin to suppress hydration warnings
      config.plugins.push({
        apply: (compiler: any) => {
          compiler.hooks.done.tap('SuppressHydrationWarnings', () => {
            if (typeof window !== 'undefined') {
              const originalError = console.error;
              console.error = (...args: any[]) => {
                const message = args[0];
                if (
                  typeof message === 'string' &&
                  (message.includes('hydrated but some attributes') ||
                    message.includes('Hydration failed') ||
                    message.includes('bis_skin_checked') ||
                    message.includes('crosspilot'))
                ) {
                  return;
                }
                originalError.apply(console, args);
              };
            }
          });
        },
      });
    }
    return config;
  },
};

export default nextConfig;
