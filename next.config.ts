import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // Suppress hydration warnings in development for browser extension conflicts
  reactStrictMode: true,
  experimental: {
    // Helps with hydration issues
    optimizePackageImports: ['@clerk/nextjs', 'lucide-react'],
  },
  // Disable source maps in development to reduce hydration noise
  productionBrowserSourceMaps: false,
};

export default nextConfig;
