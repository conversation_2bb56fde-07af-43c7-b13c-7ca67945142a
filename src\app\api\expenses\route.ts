import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { requireAuth } from '@/lib/auth';
import { query } from '@/lib/db';
import { categorizeExpense, storeExpenseEmbedding } from '@/lib/ai';
import { Expense, ApiResponse, CreateExpenseRequest } from '@/lib/types';

// Validation schema for creating expenses
const createExpenseSchema = z.object({
  amount: z.number().positive().max(999999.99),
  date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/),
  description: z.string().min(1).max(500),
  category_id: z.number().optional(),
});

// Validation schema for query parameters
const querySchema = z.object({
  page: z.string().optional().default('1'),
  limit: z.string().optional().default('20'),
  category_id: z.string().optional(),
  date_from: z.string().optional(),
  date_to: z.string().optional(),
  search: z.string().optional(),
});

export async function GET(request: NextRequest) {
  try {
    const user = await requireAuth();
    const { searchParams } = new URL(request.url);
    
    const params = querySchema.parse({
      page: searchParams.get('page') || '1',
      limit: searchParams.get('limit') || '20',
      category_id: searchParams.get('category_id'),
      date_from: searchParams.get('date_from'),
      date_to: searchParams.get('date_to'),
      search: searchParams.get('search'),
    });

    const page = parseInt(params.page);
    const limit = Math.min(parseInt(params.limit), 100); // Max 100 items per page
    const offset = (page - 1) * limit;

    // Build dynamic query
    let whereConditions = ['e.user_id = $1'];
    let queryParams: any[] = [user.id];
    let paramIndex = 2;

    if (params.category_id) {
      whereConditions.push(`e.category_id = $${paramIndex}`);
      queryParams.push(parseInt(params.category_id));
      paramIndex++;
    }

    if (params.date_from) {
      whereConditions.push(`e.date >= $${paramIndex}`);
      queryParams.push(params.date_from);
      paramIndex++;
    }

    if (params.date_to) {
      whereConditions.push(`e.date <= $${paramIndex}`);
      queryParams.push(params.date_to);
      paramIndex++;
    }

    if (params.search) {
      whereConditions.push(`e.description ILIKE $${paramIndex}`);
      queryParams.push(`%${params.search}%`);
      paramIndex++;
    }

    const whereClause = whereConditions.join(' AND ');

    // Get expenses with category information
    const expensesQuery = `
      SELECT 
        e.*,
        c.name as category_name,
        c.color as category_color
      FROM expenses e
      LEFT JOIN categories c ON e.category_id = c.id
      WHERE ${whereClause}
      ORDER BY e.date DESC, e.created_at DESC
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;

    queryParams.push(limit, offset);

    const expenses = await query<Expense & { category_name?: string; category_color?: string }>(
      expensesQuery,
      queryParams
    );

    // Get total count for pagination
    const countQuery = `
      SELECT COUNT(*) as total
      FROM expenses e
      WHERE ${whereClause}
    `;

    const countResult = await query<{ total: string }>(
      countQuery,
      queryParams.slice(0, -2) // Remove limit and offset
    );

    const total = parseInt(countResult.rows[0].total);
    const pages = Math.ceil(total / limit);

    const response: ApiResponse = {
      success: true,
      data: {
        expenses: expenses.rows.map(expense => ({
          ...expense,
          category: expense.category_name ? {
            id: expense.category_id,
            name: expense.category_name,
            color: expense.category_color,
          } : null,
        })),
        pagination: {
          page,
          limit,
          total,
          pages,
          hasNext: page < pages,
          hasPrev: page > 1,
        },
      },
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error fetching expenses:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch expenses' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const user = await requireAuth();
    const body: CreateExpenseRequest = await request.json();
    
    const validatedData = createExpenseSchema.parse(body);

    // If no category provided, try to auto-categorize
    let categoryId = validatedData.category_id;
    if (!categoryId) {
      const suggestion = await categorizeExpense(validatedData.description, user.id);
      if (suggestion) {
        categoryId = suggestion.category_id;
      }
    }

    // Create the expense
    const result = await query<Expense>(
      `INSERT INTO expenses (user_id, amount, date, description, category_id)
       VALUES ($1, $2, $3, $4, $5)
       RETURNING *`,
      [user.id, validatedData.amount, validatedData.date, validatedData.description, categoryId]
    );

    const expense = result.rows[0];

    // Store embedding for future AI improvements (async, non-blocking)
    storeExpenseEmbedding(expense.id, validatedData.description).catch(console.error);

    const response: ApiResponse<Expense> = {
      success: true,
      data: expense,
    };

    return NextResponse.json(response, { status: 201 });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation failed',
          errors: error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message,
          })),
        },
        { status: 400 }
      );
    }

    console.error('Error creating expense:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create expense' },
      { status: 500 }
    );
  }
}
