'use client';

import { useEffect, useState } from 'react';

/**
 * Hook to check if the component has hydrated on the client side
 * This helps prevent hydration mismatches caused by browser extensions
 */
export function useHydration() {
  const [isHydrated, setIsHydrated] = useState(false);

  useEffect(() => {
    setIsHydrated(true);
  }, []);

  return isHydrated;
}

/**
 * Hook to safely use window object after hydration
 */
export function useIsClient() {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  return isClient;
}
