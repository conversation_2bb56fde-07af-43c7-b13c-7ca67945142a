'use client';

import { useEffect } from 'react';

export function ConsoleSuppressor() {
  useEffect(() => {
    // Only run in development
    if (process.env.NODE_ENV !== 'development') {
      return;
    }

    // Store original console methods
    const originalError = console.error;
    const originalWarn = console.warn;

    // Override console.error to suppress hydration warnings
    console.error = (...args: any[]) => {
      const message = args[0];

      if (typeof message === 'string') {
        // List of hydration warning patterns to suppress
        const hydrationPatterns = [
          'hydrated but some attributes',
          'Hydration failed',
          'Text content does not match',
          'bis_skin_checked',
          'bis_use',
          'crosspilot',
          'data-gr-ext-installed',
          'data-new-gr-c-s-check-loaded',
          'data-bis-config',
          '__processed_',
          'data-gr-ext',
          'grammarly',
          'A tree hydrated but some attributes',
          'chrome-extension://',
          'traffic.js',
          'DashboardPage',
          'suppressHydrationWarning',
          'server rendered HTML',
          'client properties'
        ];

        // Check if this error matches any hydration warning pattern
        const isHydrationWarning = hydrationPatterns.some(pattern =>
          message.includes(pattern)
        );

        if (isHydrationWarning) {
          return; // Suppress the warning
        }
      }

      // Call original console.error for legitimate errors
      originalError.apply(console, args);
    };

    // Override console.warn for hydration warnings
    console.warn = (...args: any[]) => {
      const message = args[0];

      if (typeof message === 'string') {
        const warningPatterns = [
          'hydration',
          'server',
          'client',
          'mismatch',
          'SSR'
        ];

        const isHydrationWarning = warningPatterns.some(pattern =>
          message.toLowerCase().includes(pattern.toLowerCase())
        );

        if (isHydrationWarning) {
          return; // Suppress the warning
        }
      }

      // Call original console.warn for legitimate warnings
      originalWarn.apply(console, args);
    };

    // Cleanup function to restore original console methods
    return () => {
      console.error = originalError;
      console.warn = originalWarn;
    };
  }, []);

  return null; // This component doesn't render anything
}
