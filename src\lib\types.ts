// Database entity types
export interface User {
  id: number;
  clerk_user_id: string;
  email: string;
  first_name?: string;
  last_name?: string;
  created_at: Date;
  updated_at: Date;
}

export interface Category {
  id: number;
  name: string;
  description?: string;
  color: string;
  embedding?: number[];
  is_default: boolean;
  user_id?: number;
  created_at: Date;
  updated_at: Date;
}

export interface Expense {
  id: number;
  user_id: number;
  amount: number;
  date: Date;
  description: string;
  category_id?: number;
  embedding?: number[];
  created_at: Date;
  updated_at: Date;
  category?: Category; // Joined category data
}

export interface Budget {
  id: number;
  user_id: number;
  category_id: number;
  month: Date;
  amount: number;
  created_at: Date;
  updated_at: Date;
  category?: Category; // Joined category data
  spent?: number; // Calculated spent amount
  percentage?: number; // Calculated percentage
}

export interface Notification {
  id: number;
  user_id: number;
  type: 'budget_warning' | 'budget_exceeded' | 'info';
  title: string;
  message: string;
  read: boolean;
  created_at: Date;
}

// API request/response types
export interface CreateExpenseRequest {
  amount: number;
  date: string;
  description: string;
  category_id?: number;
}

export interface UpdateExpenseRequest {
  amount?: number;
  date?: string;
  description?: string;
  category_id?: number;
}

export interface CreateCategoryRequest {
  name: string;
  description?: string;
  color?: string;
}

export interface UpdateCategoryRequest {
  name?: string;
  description?: string;
  color?: string;
}

export interface CreateBudgetRequest {
  category_id: number;
  month: string; // YYYY-MM format
  amount: number;
}

export interface UpdateBudgetRequest {
  amount: number;
}

// Dashboard analytics types
export interface ExpenseSummary {
  total: number;
  count: number;
  average: number;
  categories: {
    category_id: number;
    category_name: string;
    total: number;
    count: number;
    color: string;
  }[];
}

export interface MonthlySpending {
  month: string;
  total: number;
  budget_total?: number;
}

export interface BudgetStatus {
  category_id: number;
  category_name: string;
  budget_amount: number;
  spent_amount: number;
  percentage: number;
  status: 'safe' | 'warning' | 'exceeded';
  color: string;
}

// AI categorization types
export interface CategorySuggestion {
  category_id: number;
  category_name: string;
  confidence: number;
  similarity: number;
}

// Form validation types
export interface FormError {
  field: string;
  message: string;
}

// API response wrapper
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  errors?: FormError[];
}

// Pagination types
export interface PaginationParams {
  page: number;
  limit: number;
  sort?: string;
  order?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Filter types
export interface ExpenseFilters {
  category_id?: number;
  date_from?: string;
  date_to?: string;
  min_amount?: number;
  max_amount?: number;
  search?: string;
}

export interface DateRange {
  from: Date;
  to: Date;
}
