'use client';

import { useEffect, useState } from 'react';

interface NoSSRProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

/**
 * Component that only renders on the client side to avoid hydration mismatches
 */
export function NoSSR({ children, fallback = null }: NoSSRProps) {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    // Suppress hydration warnings globally
    if (process.env.NODE_ENV === 'development') {
      const originalError = console.error;
      const originalWarn = console.warn;

      console.error = (...args: any[]) => {
        const message = args[0];
        if (
          typeof message === 'string' &&
          (message.includes('hydrated but some attributes') ||
            message.includes('Hydration failed') ||
            message.includes('Text content does not match') ||
            message.includes('bis_skin_checked') ||
            message.includes('crosspilot') ||
            message.includes('data-gr-ext-installed') ||
            message.includes('data-new-gr-c-s-check-loaded') ||
            message.includes('__processed_'))
        ) {
          return; // Suppress hydration warnings
        }
        originalError.apply(console, args);
      };

      console.warn = (...args: any[]) => {
        const message = args[0];
        if (
          typeof message === 'string' &&
          (message.includes('hydration') ||
            message.includes('server') ||
            message.includes('client'))
        ) {
          return; // Suppress hydration warnings
        }
        originalWarn.apply(console, args);
      };
    }

    setIsMounted(true);
  }, []);

  if (!isMounted) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

/**
 * Hook to check if component is mounted (client-side)
 */
export function useIsMounted() {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  return isMounted;
}
