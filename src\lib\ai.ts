import OpenAI from 'openai';
import { query } from './db';
import { Category, CategorySuggestion } from './types';

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

/**
 * Generate embedding for text using OpenAI
 */
export async function generateEmbedding(text: string): Promise<number[]> {
  try {
    const response = await openai.embeddings.create({
      model: 'text-embedding-ada-002',
      input: text.trim(),
    });

    return response.data[0].embedding;
  } catch (error) {
    console.error('Error generating embedding:', error);
    throw new Error('Failed to generate embedding');
  }
}

/**
 * Generate embeddings for default categories and update database
 */
export async function generateDefaultCategoryEmbeddings(): Promise<void> {
  try {
    // Get all default categories without embeddings
    const categories = await query<Category>(
      'SELECT * FROM categories WHERE is_default = true AND embedding IS NULL'
    );

    for (const category of categories.rows) {
      const embedding = await generateEmbedding(category.name + ' ' + (category.description || ''));
      
      await query(
        'UPDATE categories SET embedding = $1 WHERE id = $2',
        [JSON.stringify(embedding), category.id]
      );
    }

    console.log(`Generated embeddings for ${categories.rows.length} default categories`);
  } catch (error) {
    console.error('Error generating default category embeddings:', error);
  }
}

/**
 * Find the best matching category for an expense description
 */
export async function categorizeExpense(
  description: string,
  userId: number
): Promise<CategorySuggestion | null> {
  try {
    // Generate embedding for the expense description
    const expenseEmbedding = await generateEmbedding(description);

    // Find the most similar category using cosine similarity
    const result = await query<{
      id: number;
      name: string;
      similarity: number;
    }>(
      `SELECT 
         c.id,
         c.name,
         1 - (c.embedding <=> $1::vector) as similarity
       FROM categories c
       WHERE (c.user_id = $2 OR c.is_default = true)
         AND c.embedding IS NOT NULL
       ORDER BY c.embedding <=> $1::vector
       LIMIT 1`,
      [JSON.stringify(expenseEmbedding), userId]
    );

    if (result.rows.length === 0) {
      return null;
    }

    const match = result.rows[0];
    
    // Only return suggestion if similarity is above threshold
    const threshold = 0.7;
    if (match.similarity < threshold) {
      return null;
    }

    return {
      category_id: match.id,
      category_name: match.name,
      confidence: match.similarity,
      similarity: match.similarity,
    };
  } catch (error) {
    console.error('Error categorizing expense:', error);
    return null;
  }
}

/**
 * Get multiple category suggestions for an expense
 */
export async function getCategorySuggestions(
  description: string,
  userId: number,
  limit: number = 3
): Promise<CategorySuggestion[]> {
  try {
    const expenseEmbedding = await generateEmbedding(description);

    const result = await query<{
      id: number;
      name: string;
      similarity: number;
    }>(
      `SELECT 
         c.id,
         c.name,
         1 - (c.embedding <=> $1::vector) as similarity
       FROM categories c
       WHERE (c.user_id = $2 OR c.is_default = true)
         AND c.embedding IS NOT NULL
       ORDER BY c.embedding <=> $1::vector
       LIMIT $3`,
      [JSON.stringify(expenseEmbedding), userId, limit]
    );

    return result.rows.map(row => ({
      category_id: row.id,
      category_name: row.name,
      confidence: row.similarity,
      similarity: row.similarity,
    }));
  } catch (error) {
    console.error('Error getting category suggestions:', error);
    return [];
  }
}

/**
 * Update category embedding when category is created or updated
 */
export async function updateCategoryEmbedding(
  categoryId: number,
  name: string,
  description?: string
): Promise<void> {
  try {
    const text = name + (description ? ' ' + description : '');
    const embedding = await generateEmbedding(text);

    await query(
      'UPDATE categories SET embedding = $1 WHERE id = $2',
      [JSON.stringify(embedding), categoryId]
    );
  } catch (error) {
    console.error('Error updating category embedding:', error);
    throw error;
  }
}

/**
 * Store expense embedding for future analysis
 */
export async function storeExpenseEmbedding(
  expenseId: number,
  description: string
): Promise<void> {
  try {
    const embedding = await generateEmbedding(description);

    await query(
      'UPDATE expenses SET embedding = $1 WHERE id = $2',
      [JSON.stringify(embedding), expenseId]
    );
  } catch (error) {
    console.error('Error storing expense embedding:', error);
    // Don't throw error as this is not critical for expense creation
  }
}
