import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { requireAuth } from '@/lib/auth';
import { query } from '@/lib/db';
import { storeExpenseEmbedding } from '@/lib/ai';
import { Expense, ApiResponse, UpdateExpenseRequest } from '@/lib/types';

// Validation schema for updating expenses
const updateExpenseSchema = z.object({
  amount: z.number().positive().max(999999.99).optional(),
  date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional(),
  description: z.string().min(1).max(500).optional(),
  category_id: z.number().nullable().optional(),
});

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await requireAuth();
    const expenseId = parseInt(params.id);

    if (isNaN(expenseId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid expense ID' },
        { status: 400 }
      );
    }

    const result = await query<Expense & { category_name?: string; category_color?: string }>(
      `SELECT 
         e.*,
         c.name as category_name,
         c.color as category_color
       FROM expenses e
       LEFT JOIN categories c ON e.category_id = c.id
       WHERE e.id = $1 AND e.user_id = $2`,
      [expenseId, user.id]
    );

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Expense not found' },
        { status: 404 }
      );
    }

    const expense = result.rows[0];
    const response: ApiResponse<Expense> = {
      success: true,
      data: {
        ...expense,
        category: expense.category_name ? {
          id: expense.category_id,
          name: expense.category_name,
          color: expense.category_color,
        } : null,
      } as Expense,
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error fetching expense:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch expense' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await requireAuth();
    const expenseId = parseInt(params.id);
    const body: UpdateExpenseRequest = await request.json();

    if (isNaN(expenseId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid expense ID' },
        { status: 400 }
      );
    }

    const validatedData = updateExpenseSchema.parse(body);

    // Check if expense exists and belongs to user
    const existingExpense = await query<Expense>(
      'SELECT * FROM expenses WHERE id = $1 AND user_id = $2',
      [expenseId, user.id]
    );

    if (existingExpense.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Expense not found' },
        { status: 404 }
      );
    }

    // Build dynamic update query
    const updateFields: string[] = [];
    const updateValues: any[] = [];
    let paramIndex = 1;

    if (validatedData.amount !== undefined) {
      updateFields.push(`amount = $${paramIndex}`);
      updateValues.push(validatedData.amount);
      paramIndex++;
    }

    if (validatedData.date !== undefined) {
      updateFields.push(`date = $${paramIndex}`);
      updateValues.push(validatedData.date);
      paramIndex++;
    }

    if (validatedData.description !== undefined) {
      updateFields.push(`description = $${paramIndex}`);
      updateValues.push(validatedData.description);
      paramIndex++;
    }

    if (validatedData.category_id !== undefined) {
      updateFields.push(`category_id = $${paramIndex}`);
      updateValues.push(validatedData.category_id);
      paramIndex++;
    }

    if (updateFields.length === 0) {
      return NextResponse.json(
        { success: false, error: 'No fields to update' },
        { status: 400 }
      );
    }

    // Add updated_at field
    updateFields.push(`updated_at = CURRENT_TIMESTAMP`);

    // Add WHERE conditions
    updateValues.push(expenseId, user.id);

    const updateQuery = `
      UPDATE expenses 
      SET ${updateFields.join(', ')}
      WHERE id = $${paramIndex} AND user_id = $${paramIndex + 1}
      RETURNING *
    `;

    const result = await query<Expense>(updateQuery, updateValues);
    const updatedExpense = result.rows[0];

    // Update embedding if description changed (async, non-blocking)
    if (validatedData.description) {
      storeExpenseEmbedding(expenseId, validatedData.description).catch(console.error);
    }

    const response: ApiResponse<Expense> = {
      success: true,
      data: updatedExpense,
    };

    return NextResponse.json(response);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation failed',
          errors: error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message,
          })),
        },
        { status: 400 }
      );
    }

    console.error('Error updating expense:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update expense' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await requireAuth();
    const expenseId = parseInt(params.id);

    if (isNaN(expenseId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid expense ID' },
        { status: 400 }
      );
    }

    const result = await query(
      'DELETE FROM expenses WHERE id = $1 AND user_id = $2 RETURNING id',
      [expenseId, user.id]
    );

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Expense not found' },
        { status: 404 }
      );
    }

    const response: ApiResponse = {
      success: true,
      data: { message: 'Expense deleted successfully' },
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error deleting expense:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete expense' },
      { status: 500 }
    );
  }
}
