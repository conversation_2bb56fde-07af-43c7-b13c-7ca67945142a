# 🔐 Clerk Authentication Setup Guide

## Step 1: Create a Clerk Account

1. Go to https://dashboard.clerk.com/
2. Sign up for a free account if you don't have one
3. Verify your email address

## Step 2: Create a New Application

1. Click **"Add application"** in the Clerk Dashboard
2. Enter application name: **"Smart Budget Planner"**
3. Choose authentication methods:
   - ✅ Email
   - ✅ Google (optional)
   - ✅ GitHub (optional)
4. Click **"Create application"**

## Step 3: Get Your API Keys

1. In your Clerk Dashboard, go to **"API Keys"** (left sidebar)
2. Copy these two keys:
   - **Publishable key** (starts with `pk_test_`)
   - **Secret key** (starts with `sk_test_`)

## Step 4: Configure Your Application

### Domain Configuration
1. Go to **"Domains"** in the Clerk Dashboard
2. Add your development domain: `localhost:3002`

### Path Configuration
1. Go to **"Paths"** in the Clerk Dashboard
2. Set these paths:
   - **Sign-in URL**: `/sign-in`
   - **Sign-up URL**: `/sign-up`
   - **After sign-in URL**: `/dashboard`
   - **After sign-up URL**: `/dashboard`

## Step 5: Update Environment Variables

1. Open your `.env.local` file
2. Replace the placeholder values with your actual keys:

```bash
# Replace these with your actual keys from Clerk Dashboard
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_YOUR_ACTUAL_PUBLISHABLE_KEY_HERE
CLERK_SECRET_KEY=sk_test_YOUR_ACTUAL_SECRET_KEY_HERE
```

## Step 6: Restart the Development Server

After updating the environment variables:

```bash
# Stop the current server (Ctrl+C)
# Then restart:
npm run dev
```

## Step 7: Test Authentication

1. Visit http://localhost:3002
2. Click **"Sign In"** or **"Sign Up"** in the header
3. Create a test account
4. You should be redirected to the dashboard

## 🔍 Troubleshooting

### Error: "Unable to attribute this request to an instance"
- ✅ Check that your publishable key is correct
- ✅ Ensure the domain `localhost:3002` is added in Clerk Dashboard
- ✅ Restart the development server after changing environment variables

### Error: "Invalid publishable key"
- ✅ Make sure the key starts with `pk_test_`
- ✅ Copy the key exactly from the Clerk Dashboard (no extra spaces)

### Error: "Unauthorized"
- ✅ Check that your secret key is correct
- ✅ Make sure the key starts with `sk_test_`

## 📞 Need Help?

If you're still having issues:
1. Check the Clerk documentation: https://clerk.com/docs
2. Verify your keys are copied correctly
3. Make sure the domain is configured in Clerk Dashboard
4. Restart the development server after any changes

Once you have the correct keys, the authentication will work perfectly! 🎉
