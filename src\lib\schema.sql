-- Enable pgvector extension for AI embeddings
CREATE EXTENSION IF NOT EXISTS vector;

-- Users table for storing Clerk user data
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    clerk_user_id VARCHAR(255) UNIQUE NOT NULL,
    email VARCHAR(255) NOT NULL,
    first_name <PERSON><PERSON><PERSON><PERSON>(255),
    last_name <PERSON><PERSON><PERSON><PERSON>(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Index for fast user lookups
CREATE INDEX idx_users_clerk_user_id ON users(clerk_user_id);

-- Categories table for expense categorization
CREATE TABLE categories (
    id SERIAL PRIMARY KEY,
    name VA<PERSON>HA<PERSON>(255) NOT NULL,
    description TEXT,
    color VARCHAR(7) DEFAULT '#3B82F6', -- Hex color for UI
    embedding VECTOR(1536), -- OpenAI text-embedding-ada-002 dimensions
    is_default BOOLEAN DEFAULT FALSE,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Index for category lookups and vector similarity search
CREATE INDEX idx_categories_user_id ON categories(user_id);
CREATE INDEX idx_categories_embedding ON categories USING ivfflat (embedding vector_cosine_ops);

-- Expenses table for tracking user expenses
CREATE TABLE expenses (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    amount DECIMAL(10,2) NOT NULL CHECK (amount > 0),
    date DATE NOT NULL,
    description TEXT NOT NULL,
    category_id INTEGER REFERENCES categories(id) ON DELETE SET NULL,
    embedding VECTOR(1536), -- For AI categorization
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for expense queries
CREATE INDEX idx_expenses_user_id_date ON expenses(user_id, date DESC);
CREATE INDEX idx_expenses_category_id ON expenses(category_id);
CREATE INDEX idx_expenses_embedding ON expenses USING ivfflat (embedding vector_cosine_ops);

-- Budgets table for monthly budget allocation
CREATE TABLE budgets (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    category_id INTEGER REFERENCES categories(id) ON DELETE CASCADE,
    month DATE NOT NULL, -- First day of the month (e.g., '2023-10-01')
    amount DECIMAL(10,2) NOT NULL CHECK (amount > 0),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, category_id, month)
);

-- Index for budget queries
CREATE INDEX idx_budgets_user_id_month ON budgets(user_id, month);
CREATE INDEX idx_budgets_category_id ON budgets(category_id);

-- Notifications table for budget alerts
CREATE TABLE notifications (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    type VARCHAR(50) NOT NULL, -- 'budget_warning', 'budget_exceeded', 'info'
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Index for notification queries
CREATE INDEX idx_notifications_user_id_read ON notifications(user_id, read);
CREATE INDEX idx_notifications_created_at ON notifications(created_at DESC);

-- Insert default categories
INSERT INTO categories (name, description, color, is_default, embedding) VALUES
('Groceries', 'Food and household items', '#10B981', TRUE, NULL),
('Transportation', 'Gas, public transport, car maintenance', '#F59E0B', TRUE, NULL),
('Utilities', 'Electricity, water, internet, phone', '#3B82F6', TRUE, NULL),
('Entertainment', 'Movies, games, dining out', '#8B5CF6', TRUE, NULL),
('Healthcare', 'Medical expenses, pharmacy, insurance', '#EF4444', TRUE, NULL),
('Shopping', 'Clothing, electronics, general purchases', '#EC4899', TRUE, NULL),
('Education', 'Books, courses, training', '#06B6D4', TRUE, NULL),
('Other', 'Miscellaneous expenses', '#6B7280', TRUE, NULL);

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers to automatically update updated_at
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_categories_updated_at BEFORE UPDATE ON categories
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_expenses_updated_at BEFORE UPDATE ON expenses
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_budgets_updated_at BEFORE UPDATE ON budgets
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
