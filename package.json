{"name": "smartbudget", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "db:migrate": "node scripts/migrate.js"}, "dependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "next": "15.3.3", "@clerk/nextjs": "^6.7.0", "@clerk/themes": "^2.1.35", "pg": "^8.13.1", "openai": "^4.73.1", "pgvector": "^0.2.0", "zod": "^3.24.1", "date-fns": "^4.1.0", "recharts": "^2.13.3", "lucide-react": "^0.468.0", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-toast": "^1.2.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "tailwind-merge": "^2.5.4"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/pg": "^8.11.10", "@tailwindcss/postcss": "^4", "tailwindcss": "^4", "eslint": "^9", "eslint-config-next": "15.3.3", "@eslint/eslintrc": "^3"}}