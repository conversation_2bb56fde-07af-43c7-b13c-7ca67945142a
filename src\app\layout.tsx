import { type Metadata } from 'next'
import {
  <PERSON><PERSON><PERSON><PERSON>,
  SignIn<PERSON>utton,
  SignUp<PERSON>utton,
  SignedIn,
  SignedOut,
  UserButton,
} from '@clerk/nextjs'
import { <PERSON><PERSON><PERSON>, <PERSON>eist_Mono } from 'next/font/google'
import './globals.css'
import { ConsoleSuppressor } from '@/components/console-suppressor'

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
})

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
})

export const metadata: Metadata = {
  title: 'Smart Budget Planner',
  description: 'AI-powered personal finance management',
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <ClerkProvider>
      <html lang="en" suppressHydrationWarning>
        <head suppressHydrationWarning>
        </head>
        <body
          className={`${geistSans.variable} ${geistMono.variable} antialiased`}
          suppressHydrationWarning
        >
          <ConsoleSuppressor />
          <div suppressHydrationWarning>
            <SignedIn>
              <header className="flex justify-end items-center p-4 gap-4 h-16">
                <UserButton />
              </header>
            </SignedIn>
            {children}
          </div>
        </body>
      </html>
    </ClerkProvider>
  )
}
