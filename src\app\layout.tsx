import { type Metadata } from 'next'
import {
  <PERSON><PERSON><PERSON><PERSON>,
  SignIn<PERSON><PERSON><PERSON>,
  Sign<PERSON>p<PERSON>utton,
  SignedIn,
  SignedOut,
  UserButton,
} from '@clerk/nextjs'
import { <PERSON><PERSON><PERSON>, Geist_Mono } from 'next/font/google'
import './globals.css'

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
})

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
})

export const metadata: Metadata = {
  title: 'Smart Budget Planner',
  description: 'AI-powered personal finance management',
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <ClerkProvider>
      <html lang="en">
        <body className={`${geistSans.variable} ${geistMono.variable} antialiased`}>
          <header className="flex justify-end items-center p-4 gap-4 h-16">
            <SignedOut>
              <SignInButton />
              <SignUpB<PERSON>on />
            </SignedOut>
            <SignedIn>
              <UserButton />
            </SignedIn>
          </header>
          {children}
        </body>
      </html>
    </ClerkProvider>
  )
}
