import { type Metadata } from 'next'
import {
  <PERSON><PERSON><PERSON><PERSON>,
  SignIn<PERSON>utton,
  SignUpButton,
  SignedIn,
  SignedOut,
  UserButton,
} from '@clerk/nextjs'
import { <PERSON><PERSON><PERSON>, <PERSON>eist_Mono } from 'next/font/google'
import './globals.css'
import Script from 'next/script'

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
})

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
})

export const metadata: Metadata = {
  title: 'Smart Budget Planner',
  description: 'AI-powered personal finance management',
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <ClerkProvider>
      <html lang="en" suppressHydrationWarning>
        <head>
          <script src="/suppress-warnings.js" />
        </head>
        <body
          className={`${geistSans.variable} ${geistMono.variable} antialiased`}
          suppressHydrationW<PERSON>ning
        >
          <SignedIn>
            <header className="flex justify-end items-center p-4 gap-4 h-16">
              <UserButton />
            </header>
          </SignedIn>
          {children}
        </body>
      </html>
    </ClerkProvider>
  )
}
