import { type Metadata } from 'next'
import {
  <PERSON><PERSON><PERSON><PERSON>,
  SignInButton,
  SignUpButton,
  SignedIn,
  SignedOut,
  UserButton,
} from '@clerk/nextjs'
import { <PERSON><PERSON><PERSON>, <PERSON>eist_Mono } from 'next/font/google'
import './globals.css'
import Script from 'next/script'

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
})

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
})

export const metadata: Metadata = {
  title: 'Smart Budget Planner',
  description: 'AI-powered personal finance management',
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <ClerkProvider>
      <html lang="en" suppressHydrationWarning>
        <head>
          <script src="/suppress-warnings.js" />
          <script
            dangerouslySetInnerHTML={{
              __html: `
                // Additional immediate suppression
                if (typeof console !== 'undefined') {
                  const originalError = console.error;
                  console.error = (...args) => {
                    if (args[0] && typeof args[0] === 'string' &&
                        (args[0].includes('hydrated but some attributes') ||
                         args[0].includes('bis_skin_checked') ||
                         args[0].includes('crosspilot') ||
                         args[0].includes('A tree hydrated'))) {
                      return;
                    }
                    originalError.apply(console, args);
                  };
                }
              `
            }}
          />
        </head>
        <body
          className={`${geistSans.variable} ${geistMono.variable} antialiased`}
          suppressHydrationWarning
        >
          <div suppressHydrationWarning>
            <SignedIn>
              <header className="flex justify-end items-center p-4 gap-4 h-16">
                <UserButton />
              </header>
            </SignedIn>
            {children}
          </div>
        </body>
      </html>
    </ClerkProvider>
  )
}
