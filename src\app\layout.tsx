import { type Metadata } from 'next'
import {
  <PERSON><PERSON><PERSON><PERSON>,
  SignInButton,
  SignUpButton,
  SignedIn,
  SignedOut,
  UserButton,
} from '@clerk/nextjs'
import { <PERSON><PERSON><PERSON>, <PERSON>eist_Mono } from 'next/font/google'
import './globals.css'
import Script from 'next/script'

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
})

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
})

export const metadata: Metadata = {
  title: 'Smart Budget Planner',
  description: 'AI-powered personal finance management',
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <ClerkProvider>
      <html lang="en" suppressHydrationWarning>
        <head>
          <Script
            id="suppress-hydration-warnings"
            strategy="beforeInteractive"
            dangerouslySetInnerHTML={{
              __html: `
                if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
                  const originalError = console.error;
                  console.error = (...args) => {
                    const isHydrationWarning = args.some(arg =>
                      typeof arg === 'string' && (
                        arg.includes('hydrated but some attributes') ||
                        arg.includes('Hydration failed') ||
                        arg.includes('bis_skin_checked') ||
                        arg.includes('crosspilot') ||
                        arg.includes('data-gr-ext-installed')
                      )
                    );
                    if (!isHydrationWarning) {
                      originalError.apply(console, args);
                    }
                  };
                }
              `,
            }}
          />
        </head>
        <body
          className={`${geistSans.variable} ${geistMono.variable} antialiased`}
          suppressHydrationWarning
        >
          <SignedIn>
            <header className="flex justify-end items-center p-4 gap-4 h-16">
              <UserButton />
            </header>
          </SignedIn>
          {children}
        </body>
      </html>
    </ClerkProvider>
  )
}
