import { auth } from '@clerk/nextjs/server';
import { redirect } from 'next/navigation';
import Link from 'next/link';

export default async function DashboardPage() {
  const { userId } = await auth();

  if (!userId) {
    redirect('/sign-in');
  }

  return (
    <div className="container mx-auto px-4 py-8" suppressHydrationWarning={true}>
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-8">
          Dashboard
        </h1>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          {/* Total Expenses Card */}
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              Total Expenses
            </h3>
            <p className="text-3xl font-bold text-blue-600">$0.00</p>
            <p className="text-sm text-gray-600 dark:text-gray-400">This month</p>
          </div>

          {/* Budget Status Card */}
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              Budget Status
            </h3>
            <p className="text-3xl font-bold text-green-600">$0.00</p>
            <p className="text-sm text-gray-600 dark:text-gray-400">Remaining</p>
          </div>

          {/* Categories Card */}
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              Categories
            </h3>
            <p className="text-3xl font-bold text-purple-600">8</p>
            <p className="text-sm text-gray-600 dark:text-gray-400">Available</p>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            Quick Actions
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Link
              href="/expenses"
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-3 rounded-lg transition-colors text-center block"
            >
              Add Expense
            </Link>
            <Link
              href="/budgets"
              className="bg-green-600 hover:bg-green-700 text-white px-4 py-3 rounded-lg transition-colors text-center block"
            >
              Set Budget
            </Link>
            <Link
              href="/reports"
              className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-3 rounded-lg transition-colors text-center block"
            >
              View Reports
            </Link>
            <Link
              href="/categories"
              className="bg-orange-600 hover:bg-orange-700 text-white px-4 py-3 rounded-lg transition-colors text-center block"
            >
              Manage Categories
            </Link>
          </div>
        </div>

        {/* Recent Expenses */}
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg mt-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            Recent Expenses
          </h2>
          <div className="text-center py-8">
            <p className="text-gray-600 dark:text-gray-400">
              No expenses yet. Start by adding your first expense!
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
