import { auth, currentUser } from '@clerk/nextjs/server';
import { query } from './db';
import { User } from './types';

/**
 * Get the current authenticated user from Clerk
 */
export async function getCurrentUser() {
  return await currentUser();
}

/**
 * Get the current user's authentication info
 */
export function getAuth() {
  return auth();
}

/**
 * Get or create user in our database based on Clerk user
 */
export async function getOrCreateDbUser(): Promise<User | null> {
  const clerkUser = await getCurrentUser();
  
  if (!clerkUser) {
    return null;
  }

  try {
    // First, try to find existing user
    const existingUser = await query<User>(
      'SELECT * FROM users WHERE clerk_user_id = $1',
      [clerkUser.id]
    );

    if (existingUser.rows.length > 0) {
      return existingUser.rows[0];
    }

    // If user doesn't exist, create them
    const newUser = await query<User>(
      `INSERT INTO users (clerk_user_id, email, first_name, last_name) 
       VALUES ($1, $2, $3, $4) 
       RETURNING *`,
      [
        clerkUser.id,
        clerkUser.emailAddresses[0]?.emailAddress || '',
        clerkUser.firstName || '',
        clerkUser.lastName || ''
      ]
    );

    return newUser.rows[0];
  } catch (error) {
    console.error('Error getting or creating user:', error);
    return null;
  }
}

/**
 * Sync user data from Clerk to our database
 */
export async function syncUserFromClerk(clerkUserId: string, userData: {
  email: string;
  firstName?: string;
  lastName?: string;
}): Promise<User | null> {
  try {
    const result = await query<User>(
      `INSERT INTO users (clerk_user_id, email, first_name, last_name) 
       VALUES ($1, $2, $3, $4) 
       ON CONFLICT (clerk_user_id) 
       DO UPDATE SET 
         email = EXCLUDED.email,
         first_name = EXCLUDED.first_name,
         last_name = EXCLUDED.last_name,
         updated_at = CURRENT_TIMESTAMP
       RETURNING *`,
      [clerkUserId, userData.email, userData.firstName || '', userData.lastName || '']
    );

    return result.rows[0];
  } catch (error) {
    console.error('Error syncing user from Clerk:', error);
    return null;
  }
}

/**
 * Require authentication - throws error if user is not authenticated
 */
export async function requireAuth(): Promise<User> {
  const user = await getOrCreateDbUser();
  
  if (!user) {
    throw new Error('Authentication required');
  }
  
  return user;
}

/**
 * Check if user is authenticated
 */
export async function isAuthenticated(): Promise<boolean> {
  const clerkUser = await getCurrentUser();
  return !!clerkUser;
}

/**
 * Get user ID from authentication context
 */
export async function getUserId(): Promise<number | null> {
  const user = await getOrCreateDbUser();
  return user?.id || null;
}
